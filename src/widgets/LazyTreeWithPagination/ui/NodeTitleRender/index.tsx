import {
  FileDoneOutlined,
  FileOutlined,
  FolderOutlined,
} from '@ant-design/icons';
import { Tooltip, Typography } from 'antd';
import classNames from 'classnames';

import { TableRowData } from 'features/DataGrid';
import { useGetViewer } from 'shared/model/hooks';
import styles from './styles.module.scss';

export const NodeTitleRender = ({
  node,
  isDirectory,
}: {
  node: TreeElement | TableRowData;
  isDirectory?: boolean;
}): JSX.Element => {
  const isMain = node.isMain === 1;
  const FolderIcon = FolderOutlined;
  const FileIcon = isMain ? FileDoneOutlined : FileOutlined;

  const getViewerLink = useGetViewer();
  const canView = Boolean(node?.canView);
  const notView = node?.canView === false;
  const notLoaded = !node?.canView && node?.canView !== false;

  return (
    <div
      key={`renderTitle-${node.itemId}${node.key}`}
      className={classNames(styles.container)}
    >
      {isDirectory ? (
        <FolderIcon
          rev=""
          key={`renderFolder-${node.itemId}${node.key}`}
          style={{
            color:
              node.isFixed === 1 || node.isFixed === 3
                ? '#1890ff'
                : node.isFixed === 2
                ? '#ff4d4f'
                : 'inherit',
          }}
        />
      ) : (
        <FileIcon
          rev=""
          key={`render${isMain ? 'Main' : ''}Folder-${node.itemId}${node.key}`}
        />
      )}
      <Tooltip
        trigger={!isDirectory ? ['hover'] : []}
        title={
          canView
            ? 'Открыть в визуализаторе'
            : notView
            ? 'Нет прав для просмотра в визуализаторе'
            : 'Файл не найден'
        }
        placement="right"
      >
        <Typography.Text
          title=""
          key={`renderTitleNode-${node.itemId}${node.key}`}
          onClick={() => {
            if (node.canView && node.fileNetId && node.cardId) {
              getViewerLink(node.fileNetId, node.title as string, node.cardId);
            }
          }}
          className={classNames(
            notLoaded && styles.notLoaded,
            notView && styles.notView,
            canView && styles.canView,
          )}
        >
          {`${node.title}${
            (node?.totalCountOfLeafs ?? -1) > 0 && isDirectory
              ? ` [${node.totalCountOfLeafs}]`
              : ''
          }`}
        </Typography.Text>
      </Tooltip>
    </div>
  );
};
