import { notification } from 'antd';
import { v4 } from 'uuid';
import { NWGCConfig, NWGCLib } from 'widgets/NewWorkGroupControlTabs';
import { TableRowData } from 'features/DataGrid';
import { permissionsConfig, permissionsStore } from 'entities/Permissions';
import { apiUrls, appInstance } from 'shared/api';
import { CabinetStatuses } from 'shared/config/enums';
import {
  appErrorNotification,
  downloadFile,
  generateUrlWithQueryParams,
} from 'shared/lib';
import { createConfirmModal, useAppSelector } from 'shared/model';

const handleSendNotice = async (
  endpoint: Endpoint,
  refetch: Callback,
  body: object,
): Promise<void> => {
  try {
    const res = await appInstance.post(endpoint, body, {
      responseType: 'blob',
    });
    downloadFile(res.data, `${v4()}.zip`);
    notification.success({
      message: 'Уведомление успешно отправлено',
    });
    refetch();
  } catch (err) {
    appErrorNotification(
      'Произошла ошибка отправки уведомления ',
      err as AppError,
    );
  }
};
const handlePost = async (
  url: Endpoint,
  body: object,
  successMessage: string,
  refetch: Callback,
  errorMessage: string,
): Promise<void> => {
  try {
    await appInstance.post(url, body);
    notification.success({
      message: successMessage,
    });
    refetch();
  } catch (err) {
    appErrorNotification(errorMessage, err as AppError);
  }
};

export const useTableButtons = (
  tabEndpoint: Endpoint,
  selectedRows: TableRowData[],
  refetchTable: Callback,
  cabinetId: string,
  togglePopup: (name: keyof typeof NWGCConfig.popupsState) => void,
  cabinetStatuses: CabinetStatuses,
): AdditionalButton[] => {
  const permissions = useAppSelector(
    permissionsStore.selectors.permissionsSelector,
  );

  const canEditNotice = permissions.ap_ControlNAC.includes(
    permissionsStore.enums.Actions.EDIT_PROFILE_AT,
  );

  const canDeleteNotice = permissions.ap_ControlNAC.includes(
    permissionsStore.enums.Actions.DELETE_PROFILE_AT,
  );

  const canEditMembers = permissions.ap_ControlWG.includes(
    permissionsStore.enums.Actions.EDIT_LIST_AT,
  );

  switch (tabEndpoint) {
    case 'krg4_notice_confirm':
      return [
        {
          title: 'Создать',
          disabled: !canEditNotice,
          tooltip: !canEditNotice
            ? permissionsConfig.warnMessages.noPermissionsDefault(
                'создание уведомления',
              )
            : undefined,
          key: 'createNotification',
          type: 'primary',
          onClick: () => togglePopup('createNotification'),
        },
        {
          title: 'Отправить',
          key: 'sendNotification',
          type: 'primary',
          disabled:
            !canEditNotice ||
            NWGCLib.noticeSendValidation.isDisabled(
              selectedRows,
              cabinetStatuses,
            ),
          tooltip: !canEditNotice
            ? permissionsConfig.warnMessages.noPermissionsDefault(
                'отправку уведомления',
              )
            : NWGCLib.noticeSendValidation.message(
                selectedRows,
                cabinetStatuses,
              ),
          onClick: () => {
            createConfirmModal({
              title: 'Отправить уведомление',
              message: `Вы действительно хотите отправить уведомлени${
                selectedRows.length === 1 ? 'e' : 'я'
              } ?`,
              onConfirm: async () => {
                const body = {
                  cabinetId,
                  notice: selectedRows.map((item) => item.rowId),
                };

                await handleSendNotice(
                  apiUrls.workGroupControl.notification.sendNotification,
                  refetchTable,
                  body,
                );
              },
            });
          },
        },
        {
          title: 'Удалить',
          key: 'deleteNotification',
          danger: true,
          disabled:
            !canDeleteNotice ||
            NWGCLib.notificationDeleteValidation.isDisabled(selectedRows),
          tooltip: !canDeleteNotice
            ? permissionsConfig.warnMessages.noPermissionsDefault(
                'удаление уведомления',
              )
            : NWGCLib.notificationDeleteValidation.message(selectedRows),
          ghost: true,
          onClick: () => {
            createConfirmModal({
              title: 'Удаление уведомления',
              message: `Вы действительно хотите удалить операци${
                selectedRows.length === 1 ? 'ю' : 'и'
              } ?`,
              onConfirm: async () => {
                const postBody = {
                  cabinetId,
                  notice: selectedRows.map((item) => ({
                    noticeId: item.rowId?.noticeId,
                  })),
                };

                await handlePost(
                  apiUrls.workGroupControl.notification.deleteNotification,
                  postBody,
                  'Уведомление успешно удалено',
                  refetchTable,
                  'Ошибка удаления уведомления',
                );
              },
            });
          },
        },
      ];
    case 'krg_members':
      return [
        {
          title: 'Включить пользователя',
          disabled: !canEditMembers,
          tooltip: !canEditMembers
            ? permissionsConfig.warnMessages.noPermissionsDefault('включение')
            : undefined,
          key: 'createUser1',
          type: 'primary',
          onClick: () => togglePopup('createUser'),
        },
        {
          title: 'Изменить',
          key: 'updateUser',
          tooltip: !canEditMembers
            ? permissionsConfig.warnMessages.noPermissionsDefault(
                'редактирование',
              )
            : undefined,
          type: 'default',
          disabled:
            !canEditMembers ||
            selectedRows.length !== 1 ||
            selectedRows.some(
              (i) => i.checkboxStatus?.isDeleted || i.checkboxStatus?.isBlocked,
            ),
          onClick: () => togglePopup('updateUser'),
        },
        {
          title: 'Разблокировать',
          key: 'unblockUser1',
          type: 'default',
          tooltip: !canEditMembers
            ? permissionsConfig.warnMessages.noPermissionsDefault(
                'разблокировку',
              )
            : undefined,
          disabled:
            !canEditMembers ||
            selectedRows.length === 0 ||
            selectedRows.some(
              (i) =>
                i.checkboxStatus?.isDeleted || !i.checkboxStatus?.isBlocked,
            ),
          onClick: () => {
            createConfirmModal({
              title: 'Разблокировка пользователей',
              message: `Вы действительно хотите разблокировать ${selectedRows.map(
                (item) => item.fio,
              )} в данном КРГ?`,
              onConfirm: async () => {
                await handlePost(
                  generateUrlWithQueryParams(
                    apiUrls.workGroupControl.composition.unblockUser,
                    {
                      cabinetId,
                    },
                  ),
                  selectedRows.map((item) => item.rowId?.id),
                  'Пользователь разблокирован',
                  refetchTable,
                  'Ошибка разблокировки пользователя',
                );
              },
            });
          },
        },
        {
          title: 'Заблокировать',
          key: 'blockUser',
          danger: true,
          ghost: true,
          tooltip: !canEditMembers
            ? permissionsConfig.warnMessages.noPermissionsDefault('блокировку')
            : undefined,
          disabled:
            !canEditMembers ||
            selectedRows.length === 0 ||
            selectedRows.some(
              (i) => i.checkboxStatus?.isDeleted || i.checkboxStatus?.isBlocked,
            ),
          onClick: () => {
            createConfirmModal({
              title: 'Блокировка пользователей',
              message: `Вы действительно хотите заблокировать ${selectedRows.map(
                (item) => item.fio,
              )} в данном КРГ?`,
              onConfirm: async () => {
                await handlePost(
                  generateUrlWithQueryParams(
                    apiUrls.workGroupControl.composition.blockUser,
                    {
                      cabinetId,
                    },
                  ),
                  selectedRows.map((item) => item.rowId?.id),
                  'Пользователь заблокирован',
                  refetchTable,
                  'Ошибка блокировки пользователя',
                );
              },
            });
          },
        },
        {
          title: 'Отменить исключение',
          key: 'undeleteUser',
          type: 'default',
          tooltip: !canEditMembers
            ? permissionsConfig.warnMessages.noPermissionsDefault(
                'отмену исключения',
              )
            : undefined,
          disabled:
            !canEditMembers ||
            selectedRows.length === 0 ||
            selectedRows.some((i) => !i.checkboxStatus?.isDeleted),
          onClick: () => {
            createConfirmModal({
              title: 'Отмена исключения пользователей',
              message: `Вы действительно хотите отменить исключение ${selectedRows.map(
                (item) => item.fio,
              )} в данном КРГ?`,
              onConfirm: async () => {
                await handlePost(
                  generateUrlWithQueryParams(
                    apiUrls.workGroupControl.composition.undeleteUser,
                    {
                      cabinetId,
                    },
                  ),
                  selectedRows.map((item) => item.rowId?.id),
                  'Исключение пользователя отменено',
                  refetchTable,
                  'Ошибка отмены исключения пользователя',
                );
              },
            });
          },
        },
        {
          title: 'Исключить',
          key: 'deleteUser',
          danger: true,
          ghost: true,
          tooltip: !canEditMembers
            ? permissionsConfig.warnMessages.noPermissionsDefault('исключение')
            : undefined,
          disabled:
            !canEditMembers ||
            selectedRows.length === 0 ||
            selectedRows.some((i) => i.checkboxStatus?.isDeleted),
          onClick: () => {
            createConfirmModal({
              title: 'Исключение пользователей',
              message: `Вы действительно хотите исключить ${selectedRows.map(
                (item) => item.fio,
              )} из данного КРГ?`,
              onConfirm: async () => {
                await handlePost(
                  generateUrlWithQueryParams(
                    apiUrls.workGroupControl.composition.deleteUser,
                    {
                      cabinetId,
                    },
                  ),
                  selectedRows.map((item) => item.rowId?.id),
                  'Пользователь исключен из КРГ',
                  refetchTable,
                  'Ошибка исключения пользвателя из КРГ',
                );
              },
            });
          },
        },
      ];
    default:
      return [];
  }
};
