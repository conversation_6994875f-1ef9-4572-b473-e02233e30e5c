import { createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import { TableRowData } from 'features/DataGrid';
import { appInstance } from 'shared/api';
import {
  appErrorNotification,
  createBasicGetThunk,
  createBasicPostThunk,
  generateUrlWithQueryParams,
} from 'shared/lib';
import { ErrorWithoutShow } from 'shared/model';

export const getDataThunk = createBasicGetThunk<TableColumnsAndRows>(
  'workGroupComposition/getDataThunk',
);

export const getPermissionsSelectThunk = createBasicGetThunk<SelectData[]>(
  'workGroupComposition/getPermissionsSelectThunk',
);

export const postDataThunk = createBasicPostThunk<boolean, object>(
  'workGroupComposition/postDataThunk',
);

export const getNestedDataThunk = createAsyncThunk<
  TableRowData[],
  { cabinetId: string; endpoint: string; hfpsidUserId: string }
>(
  'workGroupComposition/getNestedDataThunk',
  async ({ hfpsidUserId, endpoint, cabinetId }, { rejectWithValue }) => {
    try {
      const { data: rows } = await appInstance.get<TableRowData[]>(
        generateUrlWithQueryParams(endpoint, {
          cabinetId,
          hfpsidUserId,
        }),
      );

      return rows;
    } catch (err) {
      if (axios.isAxiosError(err)) {
        appErrorNotification(
          'Произошла ошибка при загрузке вложенной таблицы',
          err as AppError,
        );
        return rejectWithValue(err);
      }

      throw err;
    }
  },
  {
    serializeError() {
      return new ErrorWithoutShow();
    },
  },
);
