import {
  FileDoneOutlined,
  FileOutlined,
  FolderOutlined,
} from '@ant-design/icons';
import { Typography } from 'antd';
import classNames from 'classnames';
import type { ReactNode } from 'react';

import styles from './styles.module.scss';

export const renderTitle = (node: TreeElement): ReactNode => {
  const isMain = node.isMain === 1;
  const FolderIcon = FolderOutlined;
  const FileIcon = isMain ? FileDoneOutlined : FileOutlined;

  return (
    <div
      key={`renderTitle-${node.itemId}${node.key}`}
      className={classNames(styles.container)}
    >
      {node.isDirectory ? (
        <FolderIcon
          key={`renderFolder-${node.itemId}${node.key}`}
          style={{
            color:
              node.isFixed === 1 || node.isFixed === 3
                ? '#1890ff'
                : node.isFixed === 2
                ? '#ff4d4f'
                : 'inherit',
          }}
        />
      ) : (
        <FileIcon
          key={`render${isMain ? 'Main' : ''}Folder-${node.itemId}${node.key}`}
        />
      )}
      <Typography.Text
        title=""
        key={`renderTitleNode-${node.itemId}${node.key}`}
        className={classNames(
          node.canView
            ? styles.canView
            : node.canView === false
            ? styles.notView
            : styles.notLoaded,
        )}
      >
        {`${node.title}${
          (node?.totalCountOfLeafs ?? -1) > 0
            ? ` [${node.totalCountOfLeafs}]`
            : ''
        }`}
      </Typography.Text>
    </div>
  );
};
